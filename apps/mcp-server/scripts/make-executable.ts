#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to make the built JavaScript file executable by adding a shebang line
 */

import { readFileSync, writeFileSync } from 'fs';
import { resolve } from 'path';

const filePath = process.argv[2];

if (!filePath) {
  console.error('Usage: tsx make-executable.ts <file-path>');
  process.exit(1);
}

const fullPath = resolve(filePath);

try {
  const content = readFileSync(fullPath, 'utf8');
  
  // Check if shebang already exists
  if (content.startsWith('#!')) {
    console.log(`File ${fullPath} already has a shebang line`);
    process.exit(0);
  }
  
  // Add shebang line
  const newContent = '#!/usr/bin/env node\n' + content;
  writeFileSync(fullPath, newContent);
  
  console.log(`Added shebang line to ${fullPath}`);
} catch (error) {
  console.error(`Error processing file ${fullPath}:`, error);
  process.exit(1);
}

import 'reflect-metadata';
import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { serve } from '@hono/node-server';
import { ManagedMcpServer } from './mcp-server/core/managedMcpServer.js';
import { getDataContract } from './services/dataContract.js';
import { registerBuildQueryFromDescription } from './mcp-server/tools/build_query_from_description/registration.js';
import { registerExploreTableStructure } from './mcp-server/tools/explore_table_structure/registration.js';
import { registerExplainColumnMeaning } from './mcp-server/tools/explain_column_meaning/registration.js';
import { registerExpandAbbreviations } from './mcp-server/tools/expand_abbreviations/registration.js';
import { httpTransport } from './mcp-server/transports/http.js';

async function startServer() {
  const dataContract = await getDataContract();

  const app = new Hono();
  app.use('*', cors());

  const server = new ManagedMcpServer(
    { name: 'mcp-server', version: '0.1.0' },
    {
      capabilities: {
        logging: {},
        resources: { listChanged: true },
        tools: { listChanged: true },
      },
    }
  );

  // Register tools
  registerBuildQueryFromDescription(server, dataContract);
  registerExploreTableStructure(server, dataContract);
  registerExplainColumnMeaning(server, dataContract);
  registerExpandAbbreviations(server, dataContract);

  // Setup transport
  httpTransport(app, server);

  const port = 3000;
  if (process.env.NODE_ENV !== 'test') {
    console.log(`MCP Server listening on port ${port}`);
    serve({ fetch: app.fetch, port });
  }
}

startServer().catch(error => {
  console.error('Failed to start server:', error);
  process.exit(1);
});

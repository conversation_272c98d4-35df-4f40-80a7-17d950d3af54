import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import {
  expandAbbreviationsInputSchema,
  expandAbbreviationsOutputSchema,
  expandAbbreviationsLogic,
} from './logic.js';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../../../utils/internal/errorHandler.js';
import { DataContract } from 'data-contract';

export function registerExpandAbbreviations(server: McpServer, contract: DataContract) {
  server.registerTool(
    'expand_abbreviations',
    {
      title: 'Expand Abbreviations',
      description: 'Expands an abbreviation to its full meaning.',
      inputSchema: expandAbbreviationsInputSchema as any,
      outputSchema: expandAbbreviationsOutputSchema as any,
    },
    async (params: any, callContext: Record<string, unknown>) => {
      try {
        const validatedInput = expandAbbreviationsInputSchema.parse(params);
        const result = await expandAbbreviationsLogic(contract, validatedInput);
        const validatedOutput = expandAbbreviationsOutputSchema.parse(result);

        return {
          structuredContent: validatedOutput,
          content: [
            {
              type: "text" as const,
              text: `Abbreviation expansion: ${validatedInput.abbreviation} = ${validatedOutput.expansion}`,
            },
          ],
        };
      } catch (error) {
        const handledError = ErrorHandler.handle(error as Error);
        return {
          isError: true,
          content: [{ type: "text" as const, text: `Error: ${handledError.message}` }],
          structuredContent: {
            error: handledError.message,
          },
        };
      }
    }
  );
}

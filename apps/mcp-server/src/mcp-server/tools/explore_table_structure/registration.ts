import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import {
  exploreTableStructureInputSchema,
  exploreTableStructureOutputSchema,
  exploreTableStructureLogic,
} from './logic.js';
import { <PERSON><PERSON><PERSON><PERSON>and<PERSON> } from '../../../utils/internal/errorHandler.js';
import { DataContract } from 'data-contract';

export function registerExploreTableStructure(server: McpServer, contract: DataContract) {
  server.registerTool(
    'explore_table_structure',
    {
      title: 'Explore Table Structure',
      description: 'Explores the structure of a table, returning its columns and their properties.',
      inputSchema: exploreTableStructureInputSchema as any,
      outputSchema: exploreTableStructureOutputSchema as any,
    },
    async (params: any, callContext: Record<string, unknown>) => {
      try {
        const validatedInput = exploreTableStructureInputSchema.parse(params);
        const result = await exploreTableStructureLogic(contract, validatedInput);
        const validatedOutput = exploreTableStructureOutputSchema.parse(result);

        return {
          structuredContent: validatedOutput,
          content: [
            {
              type: "text" as const,
              text: `Table structure for ${validatedInput.tableName}: ${JSON.stringify(validatedOutput, null, 2)}`,
            },
          ],
        };
      } catch (error) {
        const handledError = ErrorHandler.handle(error as Error);
        return {
          isError: true,
          content: [{ type: "text" as const, text: `Error: ${handledError.message}` }],
          structuredContent: {
            error: handledError.message,
          },
        };
      }
    }
  );
}

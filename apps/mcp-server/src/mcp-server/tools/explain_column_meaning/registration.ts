import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import {
  explainColumnMeaningInputSchema,
  explainColumnMeaningOutputSchema,
  explainColumnMeaningLogic,
} from './logic.js';
import { <PERSON>rror<PERSON>and<PERSON> } from '../../../utils/internal/errorHandler.js';
import { DataContract } from 'data-contract';

export function registerExplainColumnMeaning(server: McpServer, contract: DataContract) {
  server.registerTool(
    'explain_column_meaning',
    {
      title: 'Explain Column Meaning',
      description: 'Explains the meaning of a column, including its business name, description, and business rules.',
      inputSchema: explainColumnMeaningInputSchema as any,
      outputSchema: explainColumnMeaningOutputSchema as any,
    },
    async (params: any, callContext: Record<string, unknown>) => {
      try {
        const validatedInput = explainColumnMeaningInputSchema.parse(params);
        const result = await explainColumnMeaningLogic(contract, validatedInput);
        const validatedOutput = explainColumnMeaningOutputSchema.parse(result);

        return {
          structuredContent: validatedOutput,
          content: [
            {
              type: "text" as const,
              text: `Column meaning for ${validatedInput.tableName}.${validatedInput.columnName}: ${JSON.stringify(validatedOutput, null, 2)}`,
            },
          ],
        };
      } catch (error) {
        const handledError = ErrorHandler.handle(error as Error);
        return {
          isError: true,
          content: [{ type: "text" as const, text: `Error: ${handledError.message}` }],
          structuredContent: {
            error: handledError.message,
          },
        };
      }
    }
  );
}

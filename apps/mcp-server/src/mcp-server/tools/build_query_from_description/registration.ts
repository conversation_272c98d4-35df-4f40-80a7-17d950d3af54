import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import {
  buildQueryFromDescriptionInputSchema,
  buildQueryFromDescriptionOutputSchema,
  buildQueryFromDescriptionLogic,
} from './logic.js';
import { ErrorHandler } from '../../../utils/internal/errorHandler.js';
import { DataContract } from 'data-contract';

export function registerBuildQueryFromDescription(server: McpServer, contract: DataContract) {
  server.registerTool(
    'build_query_from_description',
    {
      title: 'Build Query From Description',
      description: 'Builds a SQL query from a natural language description.',
      inputSchema: buildQueryFromDescriptionInputSchema as any,
      outputSchema: buildQueryFromDescriptionOutputSchema as any,
    },
    async (params: any, callContext: Record<string, unknown>) => {
      try {
        const validatedInput = buildQueryFromDescriptionInputSchema.parse(params);
        const result = await buildQueryFromDescriptionLogic(contract, validatedInput);
        const validatedOutput = buildQueryFromDescriptionOutputSchema.parse(result);

        return {
          structuredContent: validatedOutput,
          content: [
            {
              type: "text" as const,
              text: `SQL Query: ${validatedOutput.sql_query}`,
            },
          ],
        };
      } catch (error) {
        const handledError = ErrorHandler.handle(error as Error);
        return {
          isError: true,
          content: [{ type: "text" as const, text: `Error: ${handledError.message}` }],
          structuredContent: {
            error: handledError.message,
          },
        };
      }
    }
  );
}

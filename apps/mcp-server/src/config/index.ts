import { z } from 'zod';
import dotenv from 'dotenv';

dotenv.config();

const configSchema = z.object({
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  MCP_TRANSPORT_TYPE: z.enum(['stdio', 'http']).default('http'),
  MCP_HTTP_PORT: z.coerce.number().default(3000),
  MCP_HTTP_HOST: z.string().default('127.0.0.1'),
  REDIS_URL: z.string().optional(),
  DATABASE_URL: z.string().optional(),
});

const config = configSchema.parse(process.env);

export default config;

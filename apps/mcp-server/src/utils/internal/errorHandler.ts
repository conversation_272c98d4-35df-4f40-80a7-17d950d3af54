import { McpError } from './mcpError.js';
import logger from './logger.js';
import { BaseErrorCode } from '../../types-global/BaseErrorCode.js';

export class ErrorHandler {
  public static handle(error: Error | McpError): { code: BaseErrorCode; message: string; context?: Record<string, unknown> } {
    if (error instanceof McpError) {
      logger.warn(`Handled MCP Error: ${error.message}`, {
        code: error.code,
        context: error.context,
      });
      return {
        code: error.code,
        message: error.message,
        context: error.context,
      };
    }

    logger.error('Unhandled Error:', error);

    // For unexpected errors, return a generic internal server error
    return {
      code: BaseErrorCode.INTERNAL_SERVER_ERROR,
      message: 'An internal server error occurred.',
    };
  }
}

import { readDataContract, DataContract } from 'data-contract';
import path from 'path';
import redis from './redis.js';

const DATA_CONTRACT_CACHE_KEY = 'mcp-server:data-contract';
const DATA_CONTRACT_CACHE_TTL_SECONDS = 3600; // 1 hour

export async function getDataContract(): Promise<DataContract> {
  let dataContract: DataContract;

  try {
    const cachedContract = await redis.get(DATA_CONTRACT_CACHE_KEY);
    if (cachedContract) {
      console.log('Data contract loaded from cache.');
      dataContract = JSON.parse(cachedContract) as DataContract;
    } else {
      console.log('Data contract cache miss. Reading from file.');
      const possiblePaths = [
        path.resolve(__dirname, '../../../../packages/data-contract/datacontract.yml'),
        path.resolve(__dirname, '../../../packages/data-contract/datacontract.yml'),
        path.resolve(process.cwd(), 'packages/data-contract/datacontract.yml'),
        path.resolve(process.cwd(), '../../packages/data-contract/datacontract.yml')
      ];
      const dataContractPath = possiblePaths.find(p => require('fs').existsSync(p));

      if (!dataContractPath) {
        throw new Error(`Data contract file not found. Tried paths: ${possiblePaths.join(', ')}`);
      }

      const contractFromFile = readDataContract(dataContractPath);
      await redis.set(
        DATA_CONTRACT_CACHE_KEY,
        JSON.stringify(contractFromFile),
        'EX',
        DATA_CONTRACT_CACHE_TTL_SECONDS
      );
      console.log('Data contract cached successfully.');
      dataContract = contractFromFile;
    }
  } catch (error) {
    if (process.env.NODE_ENV === 'test' || process.env.JEST_WORKER_ID) {
      // Provide a fallback for testing
      dataContract = { tables: {}, abbreviations: {}, tools: {} };
    } else {
      console.error('FATAL: Could not load data contract from cache or file.', error);
      process.exit(1); // Exit if we can't load the contract in a real environment
    }
  }
  return dataContract;
}

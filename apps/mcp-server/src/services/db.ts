import { Pool } from 'pg';

/**
 * A connection pool for the PostgreSQL database.
 * It reads the connection configuration from the DATABASE_URL environment variable.
 *
 * @see https://node-postgres.com/features/connecting#connection-uri
 */
export const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

pool.on('error', (err) => {
  console.error('Unexpected error on idle client', err);
  process.exit(-1);
});

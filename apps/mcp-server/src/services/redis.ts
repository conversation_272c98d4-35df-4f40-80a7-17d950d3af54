import { Redis } from 'ioredis';

// Build Redis URL from environment variables or use default
const redisHost = process.env.REDIS_HOST || 'localhost';
const redisPort = process.env.REDIS_PORT || '6379';
const redisUrl = process.env.REDIS_URL || `redis://${redisHost}:${redisPort}`;

/**
 * A singleton Redis client instance for use throughout the application.
 *
 * It uses 'lazyConnect' to prevent connection attempts until the first command is issued.
 * This is beneficial in environments where the server might start before the cache is ready,
 * and it simplifies testing by not requiring a live Redis connection for all test suites.
 */
const redis = new Redis(redisUrl, {
  lazyConnect: true,
  maxRetriesPerRequest: 3, // Prevent indefinite hangs if Redis is down
});

redis.on('error', (err: any) => {
  // Log Redis errors to the console.
  // In a production environment, this should be integrated with a proper logging service.
  console.error('Redis Client Error:', err);
});

export default redis;

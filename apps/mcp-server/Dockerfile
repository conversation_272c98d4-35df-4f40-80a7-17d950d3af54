# ---- Base ----
# Use a specific Node.js version for reproducibility.
FROM node:24.5.0-alpine AS base
WORKDIR /usr/src/app

# ---- Builder ----
# This stage builds the application and its dependencies.
FROM base AS builder
# Copy all package manifests and lock files.
COPY package.json package-lock.json* turbo.json ./
# Copy workspace package manifests
COPY apps/mcp-server/package.json ./apps/mcp-server/
COPY packages/data-contract/package.json ./packages/data-contract/
COPY packages/typescript-config/package.json ./packages/typescript-config/
# Install all dependencies.
RUN npm install
# Copy all source files for all workspaces.
COPY apps/ ./apps/
COPY packages/ ./packages/
# Build the mcp-server application.
RUN npx turbo run build --filter=mcp-server

# ---- Release ----
# This is the final stage that creates the production image.
FROM base AS release
# Copy the built application from the 'builder' stage.
COPY --from=builder /usr/src/app/apps/mcp-server/dist ./dist
# Copy the built data-contract package directly to node_modules
COPY --from=builder /usr/src/app/packages/data-contract/dist ./node_modules/data-contract/dist
COPY --from=builder /usr/src/app/packages/data-contract/package.json ./node_modules/data-contract/
COPY --from=builder /usr/src/app/packages/data-contract/datacontract.yml ./node_modules/data-contract/
# Install js-yaml dependency for data-contract
RUN npm install js-yaml --omit=dev
# Copy package.json and install other production dependencies
COPY --from=builder /usr/src/app/apps/mcp-server/package.json ./package.json
RUN npm install --omit=dev --ignore-scripts

# Expose the port the application will run on.
EXPOSE 3000
# The command to start the application.
CMD ["npm", "start"]

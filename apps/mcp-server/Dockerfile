# ---- Base ----
FROM node:24.5.0-alpine AS base
WORKDIR /usr/src/app
RUN npm install -g turbo

# ---- Pruner ----
# Use Turbo to prune the workspace for the mcp-server app
FROM base AS pruner
COPY . .
RUN turbo prune mcp-server --docker

# ---- Installer ----
# Install dependencies only when needed
FROM base AS installer
# Copy pruned lockfile and package.json files
COPY --from=pruner /usr/src/app/out/json/ .
# Install dependencies
RUN npm ci

# ---- Builder ----
# Build the application and its dependencies
FROM base AS builder
# Copy installed dependencies
COPY --from=installer /usr/src/app/ .
# Copy source code
COPY --from=pruner /usr/src/app/out/full/ .
# Build the application
RUN turbo run build --filter=mcp-server

# ---- Runner ----
# Production image, copy all the files and run
FROM base AS runner
# Copy the mcp-server package.json with scripts as the main package.json
COPY --from=builder /usr/src/app/apps/mcp-server/package.json ./package.json
# Copy the built application
COPY --from=builder /usr/src/app/apps/mcp-server/dist ./dist
# Copy the built data-contract package to node_modules
COPY --from=builder /usr/src/app/packages/data-contract/dist ./node_modules/data-contract/dist
COPY --from=builder /usr/src/app/packages/data-contract/package.json ./node_modules/data-contract/package.json
COPY --from=builder /usr/src/app/packages/data-contract/datacontract.yml ./node_modules/data-contract/datacontract.yml
# Install production dependencies for mcp-server
RUN npm install --omit=dev --ignore-scripts

# Expose the port the application will run on.
EXPOSE 3000
# The command to start the application.
CMD ["npm", "start"]

# ---- Base ----
FROM node:24.5.0-alpine AS base
WORKDIR /usr/src/app

# ---- Builder ----
FROM base AS builder
# Copy package files first for better caching
COPY package.json package-lock.json* turbo.json ./
COPY apps/mcp-server/package.json ./apps/mcp-server/
COPY packages/data-contract/package.json ./packages/data-contract/
COPY packages/typescript-config/package.json ./packages/typescript-config/
# Install dependencies
RUN npm install
# Copy source code
COPY apps/ ./apps/
COPY packages/ ./packages/
# Build the application
RUN npm run build

# ---- Release ----
FROM base AS release
# Copy the built application
COPY --from=builder /usr/src/app/apps/mcp-server/dist ./dist
# Copy the built data-contract package and its dependencies
COPY --from=builder /usr/src/app/packages/data-contract/dist ./node_modules/data-contract/dist
COPY --from=builder /usr/src/app/packages/data-contract/package.json ./node_modules/data-contract/
COPY --from=builder /usr/src/app/packages/data-contract/datacontract.yml ./node_modules/data-contract/
# Copy the data-contract node_modules (includes js-yaml)
COPY --from=builder /usr/src/app/packages/data-contract/node_modules ./node_modules/data-contract/node_modules
# Copy mcp-server package.json and install production dependencies
COPY --from=builder /usr/src/app/apps/mcp-server/package.json ./package.json
RUN npm install --omit=dev --ignore-scripts

# Expose the port the application will run on.
EXPOSE 3000
# The command to start the application.
CMD ["npm", "start"]

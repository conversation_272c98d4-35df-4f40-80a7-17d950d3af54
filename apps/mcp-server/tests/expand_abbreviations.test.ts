import { describe, it, expect } from 'vitest';
import { expandAbbreviationsLogic } from '../src/mcp-server/tools/expand_abbreviations/logic';
import { McpError } from '../src/utils/internal/mcpError';
import { BaseErrorCode } from '../src/types-global/BaseErrorCode';

describe('expandAbbreviationsLogic', () => {
  const mockContract = {
    tables: {},
    abbreviations: {
      'DB': 'Database',
      'OS': 'Operating System',
    },
    tools: {},
  };

  it('should expand a known abbreviation', async () => {
    const input = { abbreviation: 'DB' };
    const result = await expandAbbreviationsLogic(mockContract, input);
    expect(result).toEqual({ expansion: 'Database' });
  });

  it('should throw an error for an unknown abbreviation', async () => {
    const input = { abbreviation: 'API' };
    await expect(expandAbbreviationsLogic(mockContract, input)).rejects.toThrow(McpError);
    await expect(expandAbbreviationsLogic(mockContract, input)).rejects.toHaveProperty('code', BaseErrorCode.NOT_FOUND);
  });

  it('should be case-sensitive', async () => {
    const input = { abbreviation: 'db' };
    await expect(expandAbbreviationsLogic(mockContract, input)).rejects.toThrow(McpError);
  });
});

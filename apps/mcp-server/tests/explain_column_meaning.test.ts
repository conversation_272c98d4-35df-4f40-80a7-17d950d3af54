import { describe, it, expect } from 'vitest';
import { explainColumnMeaningLogic } from '../src/mcp-server/tools/explain_column_meaning/logic';
import { McpError } from '../src/utils/internal/mcpError';
import { BaseErrorCode } from '../src/types-global/BaseErrorCode';

describe('explainColumnMeaningLogic', () => {
  const mockContract = {
    tables: {
      'users': {
        columns: {
          'id': { businessName: 'ID', description: 'User ID', businessRules: [] },
          'name': { businessName: 'Name', description: 'User Name', businessRules: ['Cannot be null'] },
        },
      },
    },
    abbreviations: {},
    tools: {},
  };

  it('should return the meaning of an existing column', async () => {
    const input = { tableName: 'users', columnName: 'name' };
    const result = await explainColumnMeaningLogic(mockContract, input);
    expect(result).toEqual({
      businessName: 'Name',
      description: 'User Name',
      businessRules: ['Cannot be null'],
    });
  });

  it('should throw an error for a non-existent table', async () => {
    const input = { tableName: 'products', columnName: 'name' };
    await expect(explainColumnMeaningLogic(mockContract, input)).rejects.toThrow(McpError);
    await expect(explainColumnMeaningLogic(mockContract, input)).rejects.toHaveProperty('code', BaseErrorCode.NOT_FOUND);
  });

  it('should throw an error for a non-existent column', async () => {
    const input = { tableName: 'users', columnName: 'email' };
    await expect(explainColumnMeaningLogic(mockContract, input)).rejects.toThrow(McpError);
    await expect(explainColumnMeaningLogic(mockContract, input)).rejects.toHaveProperty('code', BaseErrorCode.NOT_FOUND);
  });
});

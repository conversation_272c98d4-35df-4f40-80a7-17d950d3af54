import { describe, it, expect } from 'vitest';
import { exploreTableStructureLogic } from '../src/mcp-server/tools/explore_table_structure/logic';
import { McpError } from '../src/utils/internal/mcpError';
import { BaseErrorCode } from '../src/types-global/BaseErrorCode';

describe('exploreTableStructureLogic', () => {
  const mockContract = {
    tables: {
      'users': {
        columns: {
          'id': { businessName: 'ID', description: 'User ID', businessRules: [] },
          'name': { businessName: 'Name', description: 'User Name', businessRules: [] },
        },
      },
    },
    abbreviations: {},
    tools: {},
  };

  it('should return the structure of an existing table', async () => {
    const input = { tableName: 'users' };
    const result = await exploreTableStructureLogic(mockContract, input);
    expect(result).toEqual(mockContract.tables.users);
  });

  it('should throw an error for a non-existent table', async () => {
    const input = { tableName: 'products' };
    await expect(exploreTableStructureLogic(mockContract, input)).rejects.toThrow(McpError);
    await expect(exploreTableStructureLogic(mockContract, input)).rejects.toHaveProperty('code', BaseErrorCode.NOT_FOUND);
  });
});

{"name": "data-contract", "version": "1.0.0", "private": true, "type": "module", "main": "./dist/src/index.js", "types": "./dist/src/index.d.ts", "scripts": {"build": "tsc -b", "validate-contract": "npm run build && node dist/src/validate-contract.js", "test": "vitest run"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.17.1", "js-yaml": "^4.1.0"}, "devDependencies": {"@types/js-yaml": "^4.0.9", "@types/node": "^20.12.12", "typescript": "^5.4.5", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4"}}